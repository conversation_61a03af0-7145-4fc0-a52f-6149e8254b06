{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 搜索效率评估\n", "\n", "本notebook用于评估SEARCH类的检索效率，包括平均耗时、整体耗时、耗时分布等情况。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/work/04_dms/idp_agent\n", "已加载环境配置: /Users/<USER>/work/04_dms/idp_agent/.env.local\n"]}], "source": ["import json\n", "import asyncio\n", "import time\n", "import httpx\n", "from typing import List, Dict\n", "import sys\n", "import os\n", "from pathlib import Path\n", "\n", "# 添加项目根目录到路径\n", "project_root = Path().cwd()\n", "sys.path.insert(0, str(project_root))\n", "print(f\"{os.path.dirname(str(project_root))}\")\n", "sys.path.append(os.path.dirname(str(project_root)))\n", "\n", "# 添加环境变量加载代码\n", "from dotenv import load_dotenv\n", "\n", "# 获取环境变量中的环境类型，默认为local\n", "env_type = os.environ.get(\"ENVIRONMENT\", \"local\")\n", "env_file = f\".env.{env_type}\"\n", "env_file_path = os.path.join(os.path.dirname(str(project_root)), env_file)\n", "\n", "# 尝试加载对应环境的配置文件\n", "if os.path.exists(env_file_path):\n", "    load_dotenv(env_file_path)\n", "    print(f\"已加载环境配置: {env_file_path}\")\n", "else:\n", "    # 如果特定环境的配置文件不存在，尝试加载默认的.env文件\n", "    if os.path.exists(\".env\"):\n", "        load_dotenv(\".env\")\n", "        print(\"已加载默认环境配置: .env\")\n", "    else:\n", "        print(f\"未找到环境配置文件: {env_file_path} 或 .env\")\n", "\n", "# 默认API地址\n", "DEFAULT_API_URL = \"http://localhost:8080/api/v1\"\n", "\n", "# # 设置环境变量（如果尚未设置）\n", "# os.environ.setdefault('SEARCH_API_URL', 'http://localhost:8000')\n", "# os.environ.setdefault('SEARCH_TOKEN', 'default_token')\n", "# os.environ.setdefault('SEARCH_DB_NAME', 'default_db')\n", "# os.environ.setdefault('RERANK_API_URL', 'http://localhost:9000')\n", "# os.environ.setdefault('RERANK_API_KEY', 'test-key')\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["共加载 28 条测试数据\n", "前5条查询:\n", "1. 器件距离板边最小距离\n", "2. PDN仿真不通过如何优化\n", "3. 分板筋距离RF Connectors要求\n", "4. 无工艺边拼板的要求有哪些\n", "5. 板振问题案例\n", "测试配置:\n", "  api_url: http://localhost:8080/api/v1/search\n", "  authorization: Bear ipd-OOabxNh6usxsPgTt6EYZHqE1\n", "  top_k: 5\n", "  top_r: 15\n", "  min_score: 0.5\n", "  user_id: test_user\n"]}], "source": ["\n", "output_file = \"search_evaluation_results.json\"\n", "input_file = \"硬工评测集.json\"\n", "\n", "# 读取测试数据\n", "with open(input_file, 'r', encoding='utf-8') as f:\n", "    test_queries = json.load(f)\n", "\n", "print(f\"共加载 {len(test_queries)} 条测试数据\")\n", "print(\"前5条查询:\")\n", "for i, item in enumerate(test_queries[:5]):\n", "    print(f\"{i+1}. {item['query']}\")\n", "    \n", "# 测试配置\n", "test_config = {\n", "    \"api_url\": \"http://localhost:8080/api/v1/search\",\n", "    \"authorization\": \"Bear ipd-OOabxNh6usxsPgTt6EYZHqE1\",\n", "    \"top_k\": 5,\n", "    \"top_r\": 15,\n", "    \"min_score\": 0.5,\n", "    \"user_id\": \"test_user\"\n", "}\n", "\n", "print(\"测试配置:\")\n", "for key, value in test_config.items():\n", "    print(f\"  {key}: {value}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始评估 28 条查询...\n", "处理第 1/28 条查询: 器件距离板边最小距离...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"硬板PCB评审要点自检表格V2.0_20240816.xlsx\", \"content\": \"硬板PCB评审要点自检表格V2.0_20240816.xlsx\\n堆叠布局\\n【内容】\\n|                 |                           \n", "处理第 2/28 条查询: PDN仿真不通过如何优化...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"PI仿真项经验总结及设计指导\", \"content\": \"PI仿真项经验总结及设计指导\\n一、PDN 概念、原理及其设计优化\\n【内容】\\n> [!TIP]\\n> PDN 优化方案：\\n- 针对 PDN DCR Fail 问题我们可以采用\\n- 调整电感位置在堆叠条件允许的\n", "处理第 3/28 条查询: 分板筋距离RF Connectors要求...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"硬板PCB评审要点自检表格\", \"content\": \"硬板PCB评审要点自检表格\\n拼版规则\\n【内容】\\n|                 | 6.5 | 连接筋远离MIC孔，距MIC孔中心3.5mm以上；                               \n", "处理第 4/28 条查询: 无工艺边拼板的要求有哪些...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"单板工艺可自动化制造性设计指导 PCBA Process DFA Guideline\", \"content\": \"单板工艺可自动化制造性设计指导 PCBA Process DFA Guideline\\n5.无板边设计规范\\n【内容】\\n| 序号 | 项目 | 要求 | 自检\n", "处理第 5/28 条查询: 板振问题案例...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"L9S项目基带复盘\", \"content\": \"L9S项目基带复盘\\n问题类（充电板振主观不达标）：\\n详细描述（必填）\\n【内容】\\nP01 测试部验收，充电板振主观大于对比机；\", \"docName\": \"L9S项目基带复盘\", \"docUrl\": \"https://x\n", "处理第 6/28 条查询: FPC点击微动失效怎么解决...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"M/N代项目点击微动深入研究\", \"content\": \"M/N代项目点击微动深入研究\\n点击微动失效 FPC 设计方向解决方案讨论  [FPC 点击微动专项研究](https://xiaomi.f.mioffice.cn/docx/doxk439dz5lAEKbKU80c\n", "处理第 7/28 条查询: 帮我总结一下P2项目今年点击微动的问题J...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"O2 P2 FPC点击微动失效问题进展\", \"content\": \"O2 P2 FPC点击微动失效问题进展\\nP2 失效情况和目前分析进展\\n**统计总表 [O2 点击微动分析统计表](https://xiaomi.f.mioffice.cn/sheets/shtk40fu\n", "处理第 8/28 条查询: 帮我找一下N2项目的点击微动解决文档...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"N2点击微动专项\", \"content\": \"N2点击微动专项\\n未失效样品：方案二未失效样品\\n【内容】\\n![](boxk4kYnMSQxki1dzOZuz9NE84e)\", \"docName\": \"N2点击微动专项\", \"docUrl\": \"https://xiaom\n", "处理第 9/28 条查询: 双层FPC的归一化叠层有哪些...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"FPC归一化叠层\", \"content\": \"FPC归一化叠层\\n首页版本信息\\n【内容】\\n| 文件名称                                                                                    \n", "处理第 10/28 条查询: 做点击微动时，点击导致BTB锡裂怎么办...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"点击微动仿真输入checklist：\", \"content\": \"点击微动仿真输入checklist：\\n点击位置示意图（含压头坐标）----先确定点击的 FPC，再确定点击位置\\n【内容】\\n[O3-BTB 松脱/点击微动](https://xiaomi.f.mioffi\n", "处理第 11/28 条查询: ACLR参数定义及设计要点...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"小米可穿戴产品_ 耳机射频测试规范_WLAN测试\", \"content\": \"小米可穿戴产品_ 耳机射频测试规范_WLAN测试\\n三、术语\\n【内容】\\n<table>\\n<tr>\\n<td>名称<br/></td><td>定义<br/></td></tr>\\n<tr>\\n\n", "处理第 12/28 条查询: N16U项目EMC问题总结...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"N16U项目EMC问题总结\", \"content\": \"N16U项目EMC问题总结\\n幻灯片 13:\\n【内容】\\n- 谢    谢\", \"docName\": \"N16U项目EMC问题总结\", \"docUrl\": \"https://xiaomi.f.mioffice.cn/\n", "处理第 13/28 条查询: N16U射频相关问题...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"N16U射频通路配置总结\", \"content\": \"N16U射频通路配置总结\\n幻灯片 61: Bring up通路问题举例\\n【内容】\\n- Bring up通路问题举例\", \"docName\": \"N16U射频通路配置总结\", \"docUrl\": \"https://x\n", "处理第 14/28 条查询: PCB设计基础知识...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"PCB应届生培训.pptx.pptx\", \"content\": \"PCB应届生培训.pptx.pptx\\n幻灯片 17\\n【内容】\\n- 手机 PCB设计原理\\n- Part 3\", \"docName\": \"PCB应届生培训.pptx.pptx\", \"docUrl\": \"h\n", "处理第 15/28 条查询: 不贴的芯片应该采用什么命名方式...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": []}, {\"collection\": \"rDataQuery\", \"refs\": [{\"title\": \"\", \"content\": \"【内容】\\n{\\\"MPNID编码\\\": \\\"1330603000025A\\\", \\\"MPNID描述\\\": \\\"CFC21490_Chuangfeixinyuan\n", "处理第 16/28 条查询: HDI板相关知识...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"Stackup_HDI-all\", \"content\": \"Stackup_HDI-all\\n首页信息\\n【内容】\\n| 文件名称 | 1.800.300.400.18 Stackup_HDI-all_V1 |  |  |  |  |\\n| --- | --- | --- \n", "处理第 17/28 条查询: 反焊盘的作用...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": []}, {\"collection\": \"rDataQuery\", \"refs\": []}]\n", "\n", "\n", "处理第 18/28 条查询: 拼板尺寸（长/宽）规范要求...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"硬板PCB评审要点自检表格V2.0_20240816.xlsx\", \"content\": \"硬板PCB评审要点自检表格V2.0_20240816.xlsx\\n拼板规范\\n【内容】\\n|               |             | 1.2  | 拼版尺寸大于5\n", "处理第 19/28 条查询: 堆叠评审检查CHECKLIST...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"项目管理\", \"content\": \"项目管理\\n五：流程内容\\n流程图说明\\n【内容】\\n|          |          |                | 5.5        | 堆叠评审 <br/>- 对堆叠方案进行详细设计评审<br/>- 组织堆叠评\n", "处理第 20/28 条查询: 走线检查CHECKLIST...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"可穿戴线体认证Checklist\", \"content\": \"可穿戴线体认证Checklist\\nSheet17\\n【内容】\\nNo valid rows found in the table.\", \"docName\": \"可穿戴线体认证Checklist\", \"docUr\n", "处理第 21/28 条查询: PDN优化方案...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"PI仿真项经验总结及设计指导\", \"content\": \"PI仿真项经验总结及设计指导\\n一、PDN 概念、原理及其设计优化\\n【内容】\\n> [!TIP]\\n> PDN 优化方案：\\n- 针对 PDN DCR Fail 问题我们可以采用\\n- 调整电感位置在堆叠条件允许的\n", "处理第 22/28 条查询: SPMI设计规范...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"mipi_SPMI_specification_v2-0\", \"content\": \"mipi_SPMI_specification_v2-0\\n1.1 Scope\\n【内容】\\nThis document describes the low- level protocol\n", "处理第 23/28 条查询: PCIE3.0的PCB设计规则...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": []}, {\"collection\": \"rDataQuery\", \"refs\": []}]\n", "\n", "\n", "处理第 24/28 条查询: FPC带EMI的设计规则...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"2024年8月份规则规范问题-20240815\", \"content\": \"2024年8月份规则规范问题-20240815\\n胜宏厂家：\\n四  FPC 异响设计规范 ou_97bd1a16ffdb1339ab9892f70733adb7\\n【内容】\\n**FPC****异\n", "处理第 25/28 条查询: HFSS如何导出FPC 3D模型...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"PCB走线寄生电容提取教程\", \"content\": \"PCB走线寄生电容提取教程\\n导出Q3D模型\\n【内容】\\n·完成HFSS 3D Layout设置- >导出Q3D模型：导入PCB- >设置叠层材料- >设置蚀刻角- >设置过孔- >切割PCB- >HFSS求解设置\\\n", "处理第 26/28 条查询: Allegro软件安装流程...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"allegro pcb规则设置\", \"content\": \"allegro pcb规则设置\\n<strong>1.4 埋入器件设置</strong>\\n【内容】\\n1.3.1、在 Allegro PCB Designer Product Choices 中的 Availab\n", "处理第 27/28 条查询: 特征阻抗计算方法...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"TDR使用\", \"content\": \"TDR使用\\n2.2.2.4 检查特征阻抗曲线\\n【内容】\\n在测量DUT之前，可以调整水平和垂直scale，观察线缆和探针的特征阻抗，\\n正常在50ohm左右。\\nESD device+同轴线缆+探头各段的特征阻抗图10所示。\\n!\n", "处理第 28/28 条查询: FPC叠层：三层119选镀和三层165选...\n", "Response: data: [{\"collection\": \"hardwareKnowledge\", \"refs\": [{\"title\": \"FPC归一化叠层\", \"content\": \"FPC归一化叠层\\n3层选镀\\n【内容】\\n|     | 阻抗控制<br>(有阻抗要求时)          | 单端50   |      |      | 单端50Ω          |      |      |   \n", "\n", "评估完成，共处理 28 条查询\n"]}], "source": ["async def evaluate_single_query(query: str, user_id: str, top_k: int, top_r: int, min_score: float) -> Dict:\n", "    \"\"\"评估单个查询的性能\"\"\"\n", "    start_time = time.time()\n", "    \n", "    try:\n", "        # 构造请求数据\n", "        payload = {\n", "            \"query\": query,\n", "            \"user_id\": user_id,\n", "            \"top_k\": top_k,\n", "            \"top_r\": top_r,\n", "            \"min_score\": min_score\n", "        }\n", "        \n", "        headers = {\n", "            \"Accept\": \"*/*\",\n", "            \"Content-Type\": \"application/json\",\n", "            \"Authorization\": test_config[\"authorization\"]\n", "        }\n", "        \n", "        # 执行搜索API调用\n", "        async with httpx.AsyncClient() as client:\n", "            response = await client.post(\n", "                test_config[\"api_url\"],\n", "                json=payload,\n", "                headers=headers,\n", "                timeout=60.0\n", "            )\n", "        \n", "        end_time = time.time()\n", "        elapsed_time = end_time - start_time\n", "        \n", "        if response.status_code == 200:\n", "            if not response.text:\n", "                raise Exception(\"No data returned from the API.\")\n", "            print(f\"Response: {response.text[:200]}\")\n", "            # 处理响应 - 可能是SSE格式或直接JSON格式\n", "            if response.text.strip().startswith('data: '):\n", "                # SSE格式\n", "                result_data = parse_sse_response(response.text)\n", "            else:\n", "                # 直接JSON格式\n", "                try:\n", "                    result_data = json.loads(response.text)\n", "                except json.JSONDecodeError:\n", "                    # 如果直接解析失败，尝试SSE解析\n", "                    result_data = parse_sse_response(response.text)\n", "            # 统计结果\n", "            total_docs = 0\n", "            if isinstance(result_data, list):\n", "                for group in result_data:\n", "                    total_docs += len(group.get('refs', []))\n", "            \n", "            return {\n", "                \"query\": query,\n", "                \"success\": True,\n", "                \"elapsed_time\": elapsed_time,\n", "                \"total_docs\": total_docs,\n", "                \"status_code\": response.status_code,\n", "                \"response_size\": len(str(result_data))\n", "            }\n", "        else:\n", "            print(f\"Error: {response.status_code}\")\n", "            return {\n", "                \"query\": query,\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"elapsed_time\": elapsed_time,\n", "                \"status_code\": response.status_code,\n", "                \"error\": f\"HTTP {response.status_code}: {response.text}\"\n", "            }\n", "            \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        end_time = time.time()\n", "        elapsed_time = end_time - start_time\n", "        \n", "        return {\n", "            \"query\": query,\n", "            \"success\": <PERSON><PERSON><PERSON>,\n", "            \"elapsed_time\": elapsed_time,\n", "            \"error\": str(e)\n", "        }\n", "        \n", "async def run_evaluation(queries: List[Dict], config: Dict) -> List[Dict]:\n", "    \"\"\"运行完整的评估\"\"\"\n", "    results = []\n", "    \n", "    print(f\"开始评估 {len(queries)} 条查询...\")\n", "    \n", "    for i, query_item in enumerate(queries):\n", "        query = query_item['query']\n", "        print(f\"处理第 {i+1}/{len(queries)} 条查询: {query[:20]}...\")\n", "        \n", "        result = await evaluate_single_query(\n", "            query=query,\n", "            user_id=config[\"user_id\"],\n", "            top_k=config[\"top_k\"],\n", "            top_r=config[\"top_r\"],\n", "            min_score=config[\"min_score\"]\n", "        )\n", "        \n", "        results.append(result)\n", "        \n", "        # 添加小延迟避免请求过于频繁\n", "        await asyncio.sleep(0.1)\n", "    \n", "    return results\n", "def parse_sse_response(response_text: str) -> List[Dict]:\n", "    \"\"\"解析SSE格式的响应\"\"\"\n", "    result_data = []\n", "    \n", "    # 按行分割响应\n", "    lines = response_text.strip().split('\\n')\n", "    \n", "    for line in lines:\n", "        line = line.strip()\n", "        if line.startswith('data: '):\n", "            # 提取data: 后面的JSON数据\n", "            json_str = line[6:]  # 去掉 'data: ' 前缀\n", "            \n", "            if json_str.strip():  # 确保不是空字符串\n", "                try:\n", "                    chunk = json.loads(json_str)\n", "                    result_data.extend(chunk)\n", "                        \n", "                except json.JSONDecodeError as e:\n", "                    print(f\"Warning: JSON解析失败: {e}, data: {json_str[:100]}...\")\n", "                    continue\n", "    \n", "    return result_data\n", "\n", "\n", "# 运行评估\n", "evaluation_results = await run_evaluation(test_queries, test_config)\n", "\n", "print(f\"\\n评估完成，共处理 {len(evaluation_results)} 条查询\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["===== 评估结果统计 =====\n", "总查询数: 28\n", "成功查询数: 28\n", "失败查询数: 0\n", "成功率: 100.00%\n", "\n", "===== 时间统计 =====\n", "总耗时: 10.16 秒\n", "平均耗时: 0.36 秒\n", "最短耗时: 0.29 秒\n", "最长耗时: 0.45 秒\n", "\n", "===== 耗时分布 =====\n", "50% 查询在 0.36 秒内完成\n", "80% 查询在 0.41 秒内完成\n", "90% 查询在 0.43 秒内完成\n", "95% 查询在 0.45 秒内完成\n"]}], "source": ["# 统计分析\n", "def analyze_results(results: List[Dict]):\n", "    \"\"\"分析评估结果\"\"\"\n", "    successful_results = [r for r in results if r[\"success\"]]\n", "    failed_results = [r for r in results if not r[\"success\"]]\n", "    \n", "    # 时间统计\n", "    times = [r[\"elapsed_time\"] for r in successful_results]\n", "    \n", "    print(\"===== 评估结果统计 =====\")\n", "    print(f\"总查询数: {len(results)}\")\n", "    print(f\"成功查询数: {len(successful_results)}\")\n", "    print(f\"失败查询数: {len(failed_results)}\")\n", "    print(f\"成功率: {len(successful_results)/len(results)*100:.2f}%\")\n", "    \n", "    if times:\n", "        print(f\"\\n===== 时间统计 =====\")\n", "        print(f\"总耗时: {sum(times):.2f} 秒\")\n", "        print(f\"平均耗时: {sum(times)/len(times):.2f} 秒\")\n", "        print(f\"最短耗时: {min(times):.2f} 秒\")\n", "        print(f\"最长耗时: {max(times):.2f} 秒\")\n", "        \n", "        # 耗时分布\n", "        times_sorted = sorted(times)\n", "        print(f\"\\n===== 耗时分布 =====\")\n", "        print(f\"50% 查询在 {times_sorted[int(len(times_sorted)*0.5)] :.2f} 秒内完成\")\n", "        print(f\"80% 查询在 {times_sorted[int(len(times_sorted)*0.8)] :.2f} 秒内完成\")\n", "        print(f\"90% 查询在 {times_sorted[int(len(times_sorted)*0.9)] :.2f} 秒内完成\")\n", "        print(f\"95% 查询在 {times_sorted[int(len(times_sorted)*0.95)] :.2f} 秒内完成\")\n", "        \n", "    if failed_results:\n", "        print(f\"\\n===== 失败查询 =====\")\n", "        for i, result in enumerate(failed_results[:5]):  # 只显示前5个失败查询\n", "            print(f\"{i+1}. {result['query'][:50]}... - 错误: {result['error']}\")\n", "            \n", "    return {\n", "        \"total\": len(results),\n", "        \"successful\": len(successful_results),\n", "        \"failed\": len(failed_results),\n", "        \"success_rate\": len(successful_results)/len(results) if results else 0,\n", "        \"total_time\": sum(times),\n", "        \"average_time\": sum(times)/len(times) if times else 0,\n", "        \"min_time\": min(times) if times else 0,\n", "        \"max_time\": max(times) if times else 0,\n", "        \"time_distribution\": {\n", "            \"50_percentile\": times_sorted[int(len(times_sorted)*0.5)] if times else 0,\n", "            \"80_percentile\": times_sorted[int(len(times_sorted)*0.8)] if times else 0,\n", "            \"90_percentile\": times_sorted[int(len(times_sorted)*0.9)] if times else 0,\n", "            \"95_percentile\": times_sorted[int(len(times_sorted)*0.95)] if times else 0,\n", "        }\n", "    }\n", "\n", "stats = analyze_results(evaluation_results)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["评估结果已保存到 search_evaluation_results.json\n", "\n", "测试配置信息:\n", "  搜索API: http://localhost:8080/api/v1/search\n", "  Authorization: Bear ipd-OOabxNh6usxsPgTt6EYZHqE1\n", "  搜索参数: top_k=5, top_r=15, min_score=0.5\n"]}], "source": ["# 保存结果到文件\n", "output_data = {\n", "    \"test_config\": test_config,\n", "    \"evaluation_results\": evaluation_results,\n", "    \"statistics\": stats\n", "}\n", "\n", "with open(output_file, \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(output_data, f, ensure_ascii=False, indent=2)\n", "\n", "print(f\"评估结果已保存到 {output_file}\")\n", "\n", "print(\"\\n测试配置信息:\")\n", "print(f\"  搜索API: {test_config['api_url']}\")\n", "print(f\"  Authorization: {test_config['authorization']}\")\n", "print(f\"  搜索参数: top_k={test_config['top_k']}, top_r={test_config['top_r']}, min_score={test_config['min_score']}\")"]}], "metadata": {"kernelspec": {"display_name": "dms", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}