# 搜索效率评估 - 修复版

import json
import asyncio
import time
import httpx
from typing import List, Dict
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path().cwd()
sys.path.insert(0, str(project_root))
print(f"{os.path.dirname(str(project_root))}")
sys.path.append(os.path.dirname(str(project_root)))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"
env_file_path = os.path.join(os.path.dirname(str(project_root)), env_file)

# 尝试加载对应环境的配置文件
if os.path.exists(env_file_path):
    load_dotenv(env_file_path)
    print(f"已加载环境配置: {env_file_path}")
else:
    # 如果特定环境的配置文件不存在，尝试加载默认的.env文件
    if os.path.exists(".env"):
        load_dotenv(".env")
        print("已加载默认环境配置: .env")
    else:
        print(f"未找到环境配置文件: {env_file_path} 或 .env")

# 默认API地址
DEFAULT_API_URL = "http://localhost:8080/api/v1"

def load_test_data():
    """加载测试数据"""
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    input_file = os.path.join(script_dir, "硬工评测集.json")
    
    # 读取测试数据
    with open(input_file, 'r', encoding='utf-8') as f:
        test_queries = json.load(f)

    print(f"共加载 {len(test_queries)} 条测试数据")
    print("前5条查询:")
    for i, item in enumerate(test_queries[:5]):
        print(f"{i+1}. {item['query']}")
    
    return test_queries

def get_test_config():
    """获取测试配置"""
    test_config = {
        "api_url": "http://localhost:8080/api/v1/search",
        "authorization": "Bear ipd-OOabxNh6usxsPgTt6EYZHqE1",
        "top_k": 20,
        "top_r": 15,
        "min_score": 0.5,
        "user_id": "test_user"
    }

    print("测试配置:")
    for key, value in test_config.items():
        print(f"  {key}: {value}")
    
    return test_config

async def evaluate_single_query(query: str, user_id: str, top_k: int, top_r: int, min_score: float, config: Dict) -> Dict:
    """评估单个查询的性能 - 修复版，正确处理SSE响应"""
    start_time = time.time()
    
    try:
        # 构造请求数据
        payload = {
            "query": query,
            "user_id": user_id,
            "top_k": top_k,
            "top_r": top_r,
            "min_score": min_score,
            "msg_id": f"eval_{int(time.time())}_{hash(query) % 10000}"
        }
        
        headers = {
            "Accept": "*/*",
            "Content-Type": "application/json",
            "Authorization": config["authorization"]
        }
        
        # 执行搜索API调用
        async with httpx.AsyncClient() as client:
            response = await client.post(
                config["api_url"],
                json=payload,
                headers=headers,
                timeout=60.0
            )
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        if response.status_code == 200:
            if not response.text:
                raise Exception("No data returned from the API.")
            
            print(f"Response preview: {response.text[:200]}...")
            
            # 处理响应 - 可能是SSE格式或直接JSON格式
            if response.text.strip().startswith('data: '):
                # SSE格式
                result_data = parse_sse_response(response.text)
            else:
                # 直接JSON格式
                try:
                    result_data = json.loads(response.text)
                except json.JSONDecodeError:
                    # 如果直接解析失败，尝试SSE解析
                    result_data = parse_sse_response(response.text)
            
            # 统计结果
            total_docs = 0
            if isinstance(result_data, list):
                for group in result_data:
                    if isinstance(group, dict):
                        refs = group.get('refs', [])
                        if isinstance(refs, str):
                            # 如果refs是JSON字符串，需要解析
                            try:
                                refs_list = json.loads(refs)
                                total_docs += len(refs_list) if isinstance(refs_list, list) else 0
                            except json.JSONDecodeError:
                                total_docs += 0
                        elif isinstance(refs, list):
                            total_docs += len(refs)
                    elif isinstance(group, list):
                        # 如果group本身就是列表，直接计算长度
                        total_docs += len(group)
            
            return {
                "query": query,
                "success": True,
                "elapsed_time": elapsed_time,
                "total_docs": total_docs,
                "status_code": response.status_code,
                "response_size": len(str(result_data))
            }
        else:
            print(f"Error: {response.status_code}")
            return {
                "query": query,
                "success": False,
                "elapsed_time": elapsed_time,
                "status_code": response.status_code,
                "error": f"HTTP {response.status_code}: {response.text}"
            }
            
    except Exception as e:
        print(f"Error: {e}")
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        return {
            "query": query,
            "success": False,
            "elapsed_time": elapsed_time,
            "error": str(e)
        }

def parse_sse_response(response_text: str) -> List[Dict]:
    """解析SSE格式的响应"""
    result_data = []
    
    # 按行分割响应
    lines = response_text.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        if line.startswith('data: '):
            # 提取data: 后面的JSON数据
            json_str = line[6:]  # 去掉 'data: ' 前缀
            
            if json_str.strip():  # 确保不是空字符串
                try:
                    chunk = json.loads(json_str)
                    result_data.extend(chunk)
                        
                except json.JSONDecodeError as e:
                    print(f"Warning: JSON解析失败: {e}, data: {json_str[:100]}...")
                    continue
    
    return result_data

async def run_evaluation(queries: List[Dict], config: Dict) -> List[Dict]:
    """运行完整的评估"""
    results = []
    
    print(f"开始评估 {len(queries)} 条查询...")
    
    for i, query_item in enumerate(queries):
        query = query_item['query']
        print(f"处理第 {i+1}/{len(queries)} 条查询: {query[:20]}...")
        
        result = await evaluate_single_query(
            query=query,
            user_id=config["user_id"],
            top_k=config["top_k"],
            top_r=config["top_r"],
            min_score=config["min_score"],
            config=config
        )
        
        results.append(result)
        
        # 添加小延迟避免请求过于频繁
        await asyncio.sleep(0.1)
    
    return results

def save_results(results: List[Dict], output_file: str = "search_evaluation_results.json"):
    """保存评估结果"""
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"评估结果已保存到: {output_file}")

def analyze_results(results: List[Dict]):
    """分析评估结果"""
    successful_results = [r for r in results if r.get("success", False)]
    failed_results = [r for r in results if not r.get("success", False)]
    
    print(f"\n=== 评估结果分析 ===")
    print(f"总查询数: {len(results)}")
    print(f"成功查询数: {len(successful_results)}")
    print(f"失败查询数: {len(failed_results)}")
    print(f"成功率: {len(successful_results)/len(results)*100:.2f}%")
    
    if successful_results:
        elapsed_times = [r["elapsed_time"] for r in successful_results]
        total_docs = [r["total_docs"] for r in successful_results]
        
        print(f"\n=== 性能统计 ===")
        print(f"平均响应时间: {sum(elapsed_times)/len(elapsed_times):.2f}秒")
        print(f"最大响应时间: {max(elapsed_times):.2f}秒")
        print(f"最小响应时间: {min(elapsed_times):.2f}秒")
        print(f"平均返回文档数: {sum(total_docs)/len(total_docs):.2f}")
        print(f"最大返回文档数: {max(total_docs)}")
        print(f"最小返回文档数: {min(total_docs)}")
    
    if failed_results:
        print(f"\n=== 失败查询 ===")
        for result in failed_results:
            print(f"查询: {result['query'][:30]}...")
            print(f"错误: {result.get('error', 'Unknown error')}")
            print("---")

async def main():
    """主函数"""
    # 加载测试数据
    test_queries = load_test_data()
    
    # 获取测试配置
    test_config = get_test_config()
    
    # 运行评估
    evaluation_results = await run_evaluation(test_queries, test_config)
    
    # 保存结果
    save_results(evaluation_results)
    
    # 分析结果
    analyze_results(evaluation_results)
    
    print(f"\n评估完成，共处理 {len(evaluation_results)} 条查询")

if __name__ == "__main__":
    asyncio.run(main()) 