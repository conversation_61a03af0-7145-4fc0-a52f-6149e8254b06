# 搜索评估脚本修复说明

## 问题描述

原始评估脚本在解析API响应时出现错误：
```
Error: Expecting value: line 1 column 1 (char 0)
```

## 问题原因

1. **API响应格式**：所有API端点（包括`/search`）都返回Server-Sent Events (SSE)格式的流式响应
2. **响应格式示例**：
   ```
   data: {"type": "reference", "content": "[{\"collection\": \"hardwareKnowledge\", \"refs\": [...]}]"}
   
   data: {"type": "reasoning", "content": "思考过程..."}
   
   data: {"type": "content", "content": "回答内容..."}
   ```
3. **原始脚本错误**：试图直接解析整个响应文本为JSON，但响应包含`data: `前缀

## 解决方案

### 方案1：使用修复版脚本（推荐）

运行修复版的评估脚本：
```bash
cd eval
python search_evaluation_fixed.py
```

### 方案2：手动修复notebook

在notebook中添加SSE解析函数：

```python
def parse_sse_response(response_text: str) -> List[Dict]:
    """解析SSE格式的响应"""
    result_data = []
    
    # 按行分割响应
    lines = response_text.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        if line.startswith('data: '):
            # 提取data: 后面的JSON数据
            json_str = line[6:]  # 去掉 'data: ' 前缀
            
            if json_str.strip():  # 确保不是空字符串
                try:
                    chunk = json.loads(json_str)
                    
                    # 如果是reference类型的chunk，提取其中的数据
                    if chunk.get("type") == "reference":
                        content = chunk.get("content", "")
                        if content:
                            try:
                                # 解析reference内容
                                ref_data = json.loads(content)
                                if isinstance(ref_data, list):
                                    result_data = ref_data
                                else:
                                    result_data.append(ref_data)
                            except json.JSONDecodeError:
                                print(f"Warning: 无法解析reference内容: {content[:100]}...")
                                continue
                    else:
                        # 其他类型的chunk，直接添加
                        result_data.append(chunk)
                        
                except json.JSONDecodeError as e:
                    print(f"Warning: JSON解析失败: {e}, data: {json_str[:100]}...")
                    continue
    
    return result_data
```

然后修改`evaluate_single_query`函数中的响应处理部分：

```python
# 替换原来的代码：
# result_data = json.loads(response.text)

# 使用新的解析函数：
result_data = parse_sse_response(response.text)
```

## 修复内容

1. **SSE响应解析**：正确处理`data: `前缀的响应格式
2. **Reference数据提取**：从`reference`类型的chunk中提取实际的搜索结果
3. **错误处理**：添加了更详细的错误处理和警告信息
4. **结果统计**：修复了文档数量统计逻辑，支持嵌套的JSON结构

## 运行修复版脚本

```bash
# 确保在项目根目录
cd /path/to/idp_agent

# 运行修复版评估脚本
python eval/search_evaluation_fixed.py
```

## 预期输出

修复后的脚本应该能够：
1. 正确解析SSE格式的API响应
2. 提取搜索结果数据
3. 统计文档数量
4. 计算响应时间
5. 生成完整的评估报告

## 注意事项

1. 确保API服务正在运行
2. 检查API地址和认证信息是否正确
3. 确保测试数据文件`硬工评测集.json`存在
4. 如果仍有问题，检查网络连接和API服务状态 