{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 搜索效率评估\n", "\n", "本notebook用于评估SEARCH类的检索效率，包括平均耗时、整体耗时、耗时分布等情况。"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/work/04_dms/idp_agent\n", "已加载环境配置: /Users/<USER>/work/04_dms/idp_agent/.env.local\n"]}, {"ename": "ImportError", "evalue": "cannot import name 'HAR<PERSON><PERSON>RE_SEARCH_COLLECTIONS' from 'config.all_search_config' (/Users/<USER>/work/04_dms/idp_agent/config/all_search_config.py)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[53], line 36\u001b[0m\n\u001b[1;32m     33\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m未找到环境配置文件: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00menv_file_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m 或 .env\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     35\u001b[0m \u001b[38;5;66;03m# 导入SEARCH类和配置\u001b[39;00m\n\u001b[0;32m---> 36\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mp<PERSON>elines\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msearch\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m SEARCH\n\u001b[1;32m     37\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mconfig\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mall_search_config\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ALL_SEARCH_MODEL_CONFIG, ALL_SEARCH_RERANK_CONFIG, ALL_SEARCH_COLLECTIONS\n", "File \u001b[0;32m~/work/04_dms/idp_agent/pipelines/search.py:13\u001b[0m\n\u001b[1;32m     11\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m logger\n\u001b[1;32m     12\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mconfig\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlogging_config\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m configure_logging\n\u001b[0;32m---> 13\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mconfig\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mall_search_config\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ALL_SEARCH_COLLECTIONS, ALL_SEARCH_MODEL_CONFIG, ALL_SEARCH_RERANK_CONFIG, DATA_SEARCH_COLLECTIONS, HARDWARE_SEARCH_COLLECTIONS\n\u001b[1;32m     14\u001b[0m configure_logging()\n\u001b[1;32m     16\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mjson\u001b[39;00m\n", "\u001b[0;31mImportError\u001b[0m: cannot import name 'HARDWARE_SEARCH_COLLECTIONS' from 'config.all_search_config' (/Users/<USER>/work/04_dms/idp_agent/config/all_search_config.py)"]}], "source": ["import json\n", "import asyncio\n", "import time\n", "from typing import List, Dict\n", "import sys\n", "import os\n", "from pathlib import Path\n", "\n", "# 添加项目根目录到路径\n", "project_root = Path().cwd().parent\n", "sys.path.insert(0, str(project_root))\n", "print(f\"{project_root}\")\n", "\n", "# 添加环境变量加载代码\n", "from dotenv import load_dotenv\n", "\n", "# 获取环境变量中的环境类型，默认为local\n", "env_type = os.environ.get(\"ENVIRONMENT\", \"local\")\n", "env_file = f\".env.{env_type}\"\n", "env_file_path = os.path.join(project_root, env_file)\n", "\n", "# 尝试加载对应环境的配置文件\n", "if os.path.exists(env_file_path):\n", "    load_dotenv(env_file_path)\n", "    print(f\"已加载环境配置: {env_file_path}\")\n", "else:\n", "    # 如果特定环境的配置文件不存在，尝试加载默认的.env文件\n", "    default_env_path = os.path.join(project_root, \".env\")\n", "    if os.path.exists(default_env_path):\n", "        load_dotenv(default_env_path)\n", "        print(\"已加载默认环境配置: .env\")\n", "    else:\n", "        print(f\"未找到环境配置文件: {env_file_path} 或 .env\")\n", "\n", "# 导入SEARCH类和配置\n", "from pipelines.search import SEARCH\n", "from config.all_search_config import ALL_SEARCH_MODEL_CONFIG, ALL_SEARCH_RERANK_CONFIG, ALL_SEARCH_COLLECTIONS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_file = \"search_evaluation_results.json\"\n", "input_file = \"硬工评测集.json\"\n", "\n", "# 读取测试数据\n", "with open(input_file, 'r', encoding='utf-8') as f:\n", "    test_queries = json.load(f)\n", "\n", "print(f\"共加载 {len(test_queries)} 条测试数据\")\n", "print(\"前5条查询:\")\n", "for i, item in enumerate(test_queries[:5]):\n", "    print(f\"{i+1}. {item['query']}\")\n", "    \n", "# 测试配置\n", "test_config = {\n", "    \"top_k\": 60,\n", "    \"top_r\": 15,\n", "    \"min_score\": 0.5,\n", "    \"user_id\": \"test_user\"\n", "}\n", "\n", "print(\"测试配置:\")\n", "for key, value in test_config.items():\n", "    print(f\"  {key}: {value}\")\n", "    \n", "print(\"搜索模型配置:\")\n", "for key, value in ALL_SEARCH_MODEL_CONFIG.items():\n", "    if key != \"default_params\":\n", "        print(f\"  {key}: {value}\")\n", "    else:\n", "        print(f\"  {key}:\")\n", "        for k, v in value.items():\n", "            print(f\"    {k}: {v}\")\n", "            \n", "print(\"重排模型配置:\")\n", "for key, value in ALL_SEARCH_RERANK_CONFIG.items():\n", "    if key != \"default_params\":\n", "        print(f\"  {key}: {value}\")\n", "    else:\n", "        print(f\"  {key}:\")\n", "        for k, v in value.items():\n", "            print(f\"    {k}: {v}\")\n", "            \n", "print(\"搜索集合配置:\")\n", "for i, collection in enumerate(ALL_SEARCH_COLLECTIONS[:5]):  # 只显示前5个\n", "    print(f\"  {i+1}. {collection}\")\n", "if len(ALL_SEARCH_COLLECTIONS) > 5:\n", "    print(f\"  ... 共 {len(ALL_SEARCH_COLLECTIONS)} 个集合\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def evaluate_single_query(query: str, user_id: str, top_k: int, top_r: int, min_score: float) -> Dict:\n", "    \"\"\"评估单个查询的性能\"\"\"\n", "    start_time = time.time()\n", "    \n", "    try:\n", "        # 创建SEARCH实例\n", "        search_instance = SEARCH(request_id=f\"eval_{int(time.time()*1000000)}\")\n", "        \n", "        # 执行搜索\n", "        results = []\n", "        async for result in search_instance.search_all_collections(\n", "            query=query,\n", "            user_id=user_id,\n", "            top_k=top_k,\n", "            top_r=top_r,\n", "            min_score=min_score\n", "        ):\n", "            results.append(result)\n", "        \n", "        end_time = time.time()\n", "        elapsed_time = end_time - start_time\n", "        \n", "        if results:\n", "            result_data = results[0]  # 获取第一个（也是唯一一个）结果\n", "            print(f\"Response: {str(result_data)[:200]}\")\n", "            \n", "            # 统计结果\n", "            total_docs = 0\n", "            if isinstance(result_data, list):\n", "                for group in result_data:\n", "                    total_docs += len(group.get('refs', []))\n", "            \n", "            return {\n", "                \"query\": query,\n", "                \"success\": True,\n", "                \"elapsed_time\": elapsed_time,\n", "                \"total_docs\": total_docs,\n", "                \"response_size\": len(str(result_data))\n", "            }\n", "        else:\n", "            return {\n", "                \"query\": query,\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"elapsed_time\": elapsed_time,\n", "                \"error\": \"No results returned from SEARCH\"\n", "            }\n", "            \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        end_time = time.time()\n", "        elapsed_time = end_time - start_time\n", "        \n", "        return {\n", "            \"query\": query,\n", "            \"success\": <PERSON><PERSON><PERSON>,\n", "            \"elapsed_time\": elapsed_time,\n", "            \"error\": str(e)\n", "        }\n", "        \n", "async def run_evaluation(queries: List[Dict], config: Dict) -> List[Dict]:\n", "    \"\"\"运行完整的评估\"\"\"\n", "    results = []\n", "    \n", "    print(f\"开始评估 {len(queries)} 条查询...\")\n", "    \n", "    for i, query_item in enumerate(queries):\n", "        query = query_item['query']\n", "        print(f\"处理第 {i+1}/{len(queries)} 条查询: {query[:20]}...\")\n", "        \n", "        result = await evaluate_single_query(\n", "            query=query,\n", "            user_id=config[\"user_id\"],\n", "            top_k=config[\"top_k\"],\n", "            top_r=config[\"top_r\"],\n", "            min_score=config[\"min_score\"]\n", "        )\n", "        \n", "        results.append(result)\n", "        \n", "        # 添加小延迟避免请求过于频繁\n", "        await asyncio.sleep(0.1)\n", "    \n", "    return results\n", "\n", "# 运行评估\n", "evaluation_results = await run_evaluation(test_queries, test_config)\n", "\n", "print(f\"\\n评估完成，共处理 {len(evaluation_results)} 条查询\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 统计分析\n", "def analyze_results(results: List[Dict]):\n", "    \"\"\"分析评估结果\"\"\"\n", "    successful_results = [r for r in results if r[\"success\"]]\n", "    failed_results = [r for r in results if not r[\"success\"]]\n", "    \n", "    # 时间统计\n", "    times = [r[\"elapsed_time\"] for r in successful_results]\n", "    \n", "    print(\"===== 评估结果统计 =====\")\n", "    print(f\"总查询数: {len(results)}\")\n", "    print(f\"成功查询数: {len(successful_results)}\")\n", "    print(f\"失败查询数: {len(failed_results)}\")\n", "    print(f\"成功率: {len(successful_results)/len(results)*100:.2f}%\")\n", "    \n", "    if times:\n", "        print(f\"\\n===== 时间统计 =====\")\n", "        print(f\"总耗时: {sum(times):.2f} 秒\")\n", "        print(f\"平均耗时: {sum(times)/len(times):.2f} 秒\")\n", "        print(f\"最短耗时: {min(times):.2f} 秒\")\n", "        print(f\"最长耗时: {max(times):.2f} 秒\")\n", "        \n", "        # 耗时分布\n", "        times_sorted = sorted(times)\n", "        print(f\"\\n===== 耗时分布 =====\")\n", "        print(f\"50% 查询在 {times_sorted[int(len(times_sorted)*0.5)] :.2f} 秒内完成\")\n", "        print(f\"80% 查询在 {times_sorted[int(len(times_sorted)*0.8)] :.2f} 秒内完成\")\n", "        print(f\"90% 查询在 {times_sorted[int(len(times_sorted)*0.9)] :.2f} 秒内完成\")\n", "        print(f\"95% 查询在 {times_sorted[int(len(times_sorted)*0.95)] :.2f} 秒内完成\")\n", "        \n", "    if failed_results:\n", "        print(f\"\\n===== 失败查询 =====\")\n", "        for i, result in enumerate(failed_results[:5]):  # 只显示前5个失败查询\n", "            print(f\"{i+1}. {result['query'][:50]}... - 错误: {result['error']}\")\n", "            \n", "    return {\n", "        \"total\": len(results),\n", "        \"successful\": len(successful_results),\n", "        \"failed\": len(failed_results),\n", "        \"success_rate\": len(successful_results)/len(results) if results else 0,\n", "        \"total_time\": sum(times),\n", "        \"average_time\": sum(times)/len(times) if times else 0,\n", "        \"min_time\": min(times) if times else 0,\n", "        \"max_time\": max(times) if times else 0,\n", "        \"time_distribution\": {\n", "            \"50_percentile\": times_sorted[int(len(times_sorted)*0.5)] if times else 0,\n", "            \"80_percentile\": times_sorted[int(len(times_sorted)*0.8)] if times else 0,\n", "            \"90_percentile\": times_sorted[int(len(times_sorted)*0.9)] if times else 0,\n", "            \"95_percentile\": times_sorted[int(len(times_sorted)*0.95)] if times else 0,\n", "        }\n", "    }\n", "\n", "stats = analyze_results(evaluation_results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存结果到文件\n", "output_data = {\n", "    \"test_config\": test_config,\n", "    \"evaluation_results\": evaluation_results,\n", "    \"statistics\": stats\n", "}\n", "\n", "with open(output_file, \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(output_data, f, ensure_ascii=False, indent=2)\n", "\n", "print(f\"评估结果已保存到 {output_file}\")\n", "\n", "print(\"\\n测试配置信息:\")\n", "print(f\"  搜索参数: top_k={test_config['top_k']}, top_r={test_config['top_r']}, min_score={test_config['min_score']}\")\n", "print(f\"  用户ID: {test_config['user_id']}\")"]}], "metadata": {"kernelspec": {"display_name": "dms", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}